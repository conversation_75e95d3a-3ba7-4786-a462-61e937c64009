CREATE TABLE "JIGEXIONGDI"."SYS_DEPT"
(
"DEPT_ID" BIGINT IDENTITY(484, 1) NOT NULL,
"PARENT_ID" BIGINT DEFAULT 0,
"ANCESTORS" VARCHAR(50) DEFAULT '',
"DEPT_NAME" VARCHAR(100) DEFAULT '',
"ORDER_NUM" INT DEFAULT 0,
"LEADER" VARCHAR(20),
"PHONE" VARCHAR(11),
"EMAIL" VARCHAR(50),
"STATUS" CHAR(1) DEFAULT '0',
"DEL_FLAG" CHAR(1) DEFAULT '0',
"CREATE_BY" VARCHAR(64) DEFAULT '',
"CREATE_TIME" TIMESTAMP(0),
"UPDATE_BY" VARCHAR(64) DEFAULT '',
"UPDATE_TIME" TIMESTAMP(0),
"TYPE" INT,
"region" VARCHAR(50),
"training_facilities_rating" INT,
"commander_id" VARCHAR(50),
"training_method_type" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("DEPT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.SYS_DEPT IS '部门表';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."ANCESTORS" IS '祖级列表';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."commander_id" IS '指挥员编号';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."CREATE_BY" IS '创建者';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."DEPT_ID" IS '部门id';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."DEPT_NAME" IS '部门名称';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."EMAIL" IS '邮箱';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."LEADER" IS '负责人';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."ORDER_NUM" IS '显示顺序';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."PARENT_ID" IS '父部门id';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."PHONE" IS '联系电话';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."region" IS '驻地区域(南方/北方)';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."STATUS" IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."training_facilities_rating" IS '训练场地设施评级(1-5)';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."training_method_type" IS '组训方法类型';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."TYPE" IS '部队类型（内卫、机动、海警）';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DEPT."UPDATE_TIME" IS '更新时间';


CREATE OR REPLACE UNIQUE  INDEX "JIGEXIONGDI"."INDEX290416752250900" ON "JIGEXIONGDI"."SYS_DEPT"("DEPT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "JIGEXIONGDI"."SYS_DICT_DATA"
(
"DICT_CODE" BIGINT IDENTITY(100, 1) NOT NULL,
"DICT_SORT" INT DEFAULT 0,
"DICT_LABEL" VARCHAR(100) DEFAULT '',
"DICT_VALUE" VARCHAR(100) DEFAULT '',
"DICT_TYPE" VARCHAR(100) DEFAULT '',
"CSS_CLASS" VARCHAR(100),
"LIST_CLASS" VARCHAR(100),
"IS_DEFAULT" CHAR(1) DEFAULT 'N',
"STATUS" CHAR(1) DEFAULT '0',
"CREATE_BY" VARCHAR(64) DEFAULT '',
"CREATE_TIME" TIMESTAMP(0),
"UPDATE_BY" VARCHAR(64) DEFAULT '',
"UPDATE_TIME" TIMESTAMP(0),
"REMARK" VARCHAR(500),
NOT CLUSTER PRIMARY KEY("DICT_CODE")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.SYS_DICT_DATA IS '字典数据表';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."CREATE_BY" IS '创建者';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."CSS_CLASS" IS '样式属性（其他样式扩展）';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."DICT_CODE" IS '字典编码';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."DICT_LABEL" IS '字典标签';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."DICT_SORT" IS '字典排序';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."DICT_TYPE" IS '字典类型';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."DICT_VALUE" IS '字典键值';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."IS_DEFAULT" IS '是否默认（Y是 N否）';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."LIST_CLASS" IS '表格回显样式';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."REMARK" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."STATUS" IS '状态（0正常 1停用）';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_DATA."UPDATE_TIME" IS '更新时间';


CREATE OR REPLACE UNIQUE  INDEX "JIGEXIONGDI"."INDEX290416535342200" ON "JIGEXIONGDI"."SYS_DICT_DATA"("DICT_CODE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "JIGEXIONGDI"."SYS_DICT_TYPE"
(
"DICT_ID" BIGINT IDENTITY(100, 1) NOT NULL,
"DICT_NAME" VARCHAR(100) DEFAULT '',
"DICT_TYPE" VARCHAR(100) DEFAULT '',
"STATUS" CHAR(1) DEFAULT '0',
"CREATE_BY" VARCHAR(64) DEFAULT '',
"CREATE_TIME" TIMESTAMP(0),
"UPDATE_BY" VARCHAR(64) DEFAULT '',
"UPDATE_TIME" TIMESTAMP(0),
"REMARK" VARCHAR(500),
NOT CLUSTER PRIMARY KEY("DICT_ID"),
CONSTRAINT "DICT_TYPEn" UNIQUE("DICT_TYPE")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.SYS_DICT_TYPE IS '字典类型表';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."CREATE_BY" IS '创建者';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."DICT_ID" IS '字典主键';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."DICT_NAME" IS '字典名称';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."DICT_TYPE" IS '字典类型';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."REMARK" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."STATUS" IS '状态（0正常 1停用）';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN JIGEXIONGDI.SYS_DICT_TYPE."UPDATE_TIME" IS '更新时间';


CREATE OR REPLACE UNIQUE  INDEX "JIGEXIONGDI"."INDEX290416430059600" ON "JIGEXIONGDI"."SYS_DICT_TYPE"("DICT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_APP"
(
"ID" BIGINT NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"SN" VARCHAR(255 CHAR),
"BRIEF_INFO" TEXT,
"APP_TYPE" VARCHAR(50 CHAR),
"IS_BUILD_IN" TINYINT,
"ENABLE" TINYINT,
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_APP IS 'AI应用表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."APP_TYPE" IS '应用类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."BRIEF_INFO" IS '简要信息';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."ENABLE" IS '是否启用';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."IS_BUILD_IN" IS '是否是内置app';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."NAME" IS '应用名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP."SN" IS 'SN';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_APP_CONFIG"
(
"ID" BIGINT NOT NULL,
"LINK_ID" BIGINT NOT NULL,
"LINK_TYPE" VARCHAR(255 CHAR),
"CONFIG_VALUE" TEXT,
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_APP_CONFIG IS 'AI应用配置表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_CONFIG."CONFIG_VALUE" IS '配置值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_CONFIG."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_CONFIG."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_CONFIG."LINK_ID" IS '关联ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_CONFIG."LINK_TYPE" IS '关联类型,app,session,node';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_APP_DESIGN"
(
"ID" BIGINT NOT NULL,
"APP_ID" BIGINT NOT NULL,
"NODES" text,
"EDGES" TEXT,
"UPDATE_TIME" TIMESTAMP(0) NOT NULL,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_APP_DESIGN IS 'app流程设计表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_DESIGN."APP_ID" IS '应用id';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_DESIGN."EDGES" IS '关联关系定义';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_DESIGN."ID" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_DESIGN."NODES" IS '节点定义';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_DESIGN."UPDATE_TIME" IS '更新时间';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_APP_MESSAGE"
(
"ID" BIGINT NOT NULL,
"APP_SESSION_ID" BIGINT NOT NULL,
"APP_ID" BIGINT NOT NULL,
"CONTENT" TEXT,
"DOCS" VARCHAR(500 CHAR),
"IMGS" VARCHAR(500 CHAR),
"LINK_ID" BIGINT,
"ROLE" VARCHAR(50 CHAR),
"STATUS" VARCHAR(20 CHAR),
"ERROR_MSG" VARCHAR(255 CHAR),
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
"THINKING" CLOB,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE IS 'App消息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."APP_ID" IS '应用Id';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."APP_SESSION_ID" IS '关联 app会话表的主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."CONTENT" IS '消息内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."DOCS" IS '关联的aidocId';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."ERROR_MSG" IS '错误消息原因';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."IMGS" IS '关联的图片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."LINK_ID" IS '关联id,消息双向关联';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."ROLE" IS '系统角色';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."STATUS" IS '消息状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_MESSAGE."THINKING" IS '思考过程';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_APP_NODE"
(
"ID" BIGINT NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"APP_ID" BIGINT NOT NULL,
"AFTERS" VARCHAR(500 CHAR),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_APP_NODE IS '应用节点定义';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_NODE."AFTERS" IS '后续节点';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_NODE."APP_ID" IS '所属的应用';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_NODE."ID" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_NODE."TYPE" IS '节点类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_APP_SESSION"
(
"ID" BIGINT DEFAULT 0 NOT NULL,
"NAME" VARCHAR(255 CHAR),
"APP_ID" BIGINT NOT NULL,
"IS_DEBUG" TINYINT DEFAULT 0,
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_APP_SESSION IS 'appSession会话表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_SESSION."APP_ID" IS '应用Id';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_SESSION."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_SESSION."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_SESSION."IS_DEBUG" IS '是否是debug模式产生的会话';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_APP_SESSION."NAME" IS '会话名称';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_DOC"
(
"ID" BIGINT NOT NULL,
"CONTENT" TEXT NOT NULL,
"KNOWLEDGE_BASE_ID" BIGINT,
"LINK_TYPE" VARCHAR(50 CHAR),
"LINK_ID" BIGINT,
"FILE_ID" BIGINT,
"DOC_ID" BIGINT,
"DOC_TYPE" VARCHAR(100 CHAR),
"DATA_TYPE" VARCHAR(50 CHAR) NOT NULL,
"EMBED_TIME" TIMESTAMP(0),
"TRAIN_TIME" TIMESTAMP(0),
"DOC_STATUS" INT,
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_DOC IS 'AI文档表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."CONTENT" IS '文档标题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."DATA_TYPE" IS '数据类型:customText:自定义文本,filePiece:文件块';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."DOC_ID" IS '基于doc又进行训练';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."DOC_STATUS" IS '文档状态:100:未训练,200:训练中,300:训练成功,400:训练失败,500:向量化中,600:向量化成功,700:向量化失败';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."DOC_TYPE" IS '文件类型,例如:normal:普通文档,QA:QA文档';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."EMBED_TIME" IS '向量化时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."FILE_ID" IS '文件ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."ID" IS '文档ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."KNOWLEDGE_BASE_ID" IS '知识库ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."LINK_ID" IS '关联的answerId';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."LINK_TYPE" IS '关联的类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC."TRAIN_TIME" IS '训练时间';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_DOC_ANSWER"
(
"ID" BIGINT NOT NULL,
"ANSWER" TEXT,
"CREATE_TIME" TIMESTAMP(0),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_DOC_ANSWER."ANSWER" IS '答案';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_FILE"
(
"ID" BIGINT NOT NULL,
"KNOWLEDGE_BASE_ID" BIGINT NOT NULL,
"FILE_PATH" VARCHAR(512 CHAR) NOT NULL,
"FILE_NAME" VARCHAR(255 CHAR) NOT NULL,
"FILE_SIZE" BIGINT NOT NULL,
"FILE_TYPE" VARCHAR(64 CHAR) NOT NULL,
"FILE_STATUS" INT NOT NULL,
"FILE_STATUS_MSG" VARCHAR(500 CHAR),
"DOC_TYPE" VARCHAR(100 CHAR),
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
"TRAINING_TIME" TIMESTAMP(0),
"EMBED_TIME" TIMESTAMP(0),
"SPLIT_RULE" VARCHAR(255 CHAR),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_FILE IS 'AI文件表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."DOC_TYPE" IS '文件类型,例如:normal:普通文档,QA:QA文档';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."EMBED_TIME" IS '向量化时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."FILE_NAME" IS '文件名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."FILE_PATH" IS '文件地址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."FILE_SIZE" IS '文件大小';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."FILE_STATUS" IS '文件状态,100:普通文档未拆分,QA文档未训练,200:普通文档拆分中,QA文档未训练中,300:普通文档拆分成功,QA文档训练成功,400:普通文档拆分失败,QA文档训练失败,500:向量化中,600:向量化成功,700:向量化失败';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."FILE_STATUS_MSG" IS '文件状态信息，一般为错误信息';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."FILE_TYPE" IS '文件类型,例如:txt,excel,pdf等';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."KNOWLEDGE_BASE_ID" IS '知识库ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."SPLIT_RULE" IS '拆分规则';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FILE."TRAINING_TIME" IS '训练时间';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_FUNCTION_PARAM"
(
"ID" BIGINT NOT NULL,
"AI_FUNCTION_ID" BIGINT NOT NULL,
"PARAM_NAME" VARCHAR(255 CHAR) NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"BRIEF_INFO" TEXT,
"DEFAULT_VALUE" VARCHAR(255 CHAR),
"IS_REQUIRED" TINYINT DEFAULT 0 NOT NULL,
"SORT_RANK" INT,
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM IS 'AI函数参数表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."AI_FUNCTION_ID" IS 'AI函数ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."BRIEF_INFO" IS '参数描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."DEFAULT_VALUE" IS '默认值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."IS_REQUIRED" IS '是否必填';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."PARAM_NAME" IS '参数名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."SORT_RANK" IS '参数顺序';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_PARAM."TYPE" IS '参数值类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_FUNCTION_RESOURCE"
(
"ID" BIGINT NOT NULL,
"SN" VARCHAR(255 CHAR) NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"BRIEF_INFO" TEXT,
"SERVICE_CLASS" VARCHAR(255 CHAR) NOT NULL,
"SERVICE_METHOD" VARCHAR(255 CHAR) NOT NULL,
"PARAM_COUNT" INT,
"ENABLE" TINYINT DEFAULT 1 NOT NULL,
"CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE IS 'AI函数表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."BRIEF_INFO" IS '功能描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."ENABLE" IS '是否启用';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."PARAM_COUNT" IS '参数个数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."SERVICE_CLASS" IS '所属类名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."SERVICE_METHOD" IS '方法名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."SN" IS '编码';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_FUNCTION_RESOURCE."TYPE" IS '类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_KNOWLEDGE_BASE"
(
"ID" BIGINT NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"VDB_RESOURCE_ID" BIGINT,
"VDB_RESOURCE_TYPE" VARCHAR(255 CHAR),
"BRIEF_INFO" TEXT,
"IS_FULL_TEXT_SEARCHING_ENABLE" TINYINT DEFAULT 0,
"ENABLE" TINYINT DEFAULT 1,
"CREATE_TIME" TIMESTAMP(0),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE IS 'AI知识库';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."BRIEF_INFO" IS '简介';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."ENABLE" IS '是否开启';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."ID" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."IS_FULL_TEXT_SEARCHING_ENABLE" IS '是否启用全文搜索';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."NAME" IS '名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."VDB_RESOURCE_ID" IS 'VDB资源的主键id';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_KNOWLEDGE_BASE."VDB_RESOURCE_TYPE" IS 'VDB资源的type';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_AI_RESOURCE"
(
"ID" BIGINT NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR),
"DEFAULT_MODEL" VARCHAR(255 CHAR),
"API_KEY" VARCHAR(255 CHAR),
"API_SECRET" VARCHAR(255 CHAR),
"API_URL" VARCHAR(255 CHAR),
"LIMITS" VARCHAR(255 CHAR),
"ENABLE" BIT NOT NULL,
"CREATE_TIME" TIMESTAMP(0),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_AI_RESOURCE IS 'AI资源';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."API_KEY" IS '大模型需要的APIKEY';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."API_SECRET" IS '大模型需要的SECRET KEY';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."API_URL" IS '接口地址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."DEFAULT_MODEL" IS '默认模型型号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."ENABLE" IS '是否启用';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."ID" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."LIMITS" IS '频率限制，格式[{unit:''day'', times: 10}]';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."NAME" IS '资源名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_AI_RESOURCE."TYPE" IS '类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ASSESSMENT_ITEMS"
(
"assessment_id" INT IDENTITY(1, 1) NOT NULL,
"assessment_name" VARCHAR(100) NOT NULL,
"assessment_type" VARCHAR(50),
"max_score" DECIMAL(5,2),
"pass_score" DECIMAL(5,2),
"weight" DECIMAL(5,2),
"create_time" VARCHAR(50),
"update_time" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("assessment_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS IS '考核项目表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."assessment_id" IS '编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."assessment_name" IS '考核项目名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."assessment_type" IS '考核类型(体能/技能/战术等)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."max_score" IS '满分值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."pass_score" IS '及格分数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."update_time" IS '修改时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_ITEMS."weight" IS '在总评中的权重';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ASSESSMENT_RESULTS"
(
"result_id" INT IDENTITY(1, 1) NOT NULL,
"ygxx_id" VARCHAR(50) NOT NULL,
"assessment_id" VARCHAR(50) NOT NULL,
"score" DECIMAL(5,2),
"assessment_date" VARCHAR(20) NOT NULL,
"assessment_time" VARCHAR(20),
"pass_status" VARCHAR(20),
"create_time" VARCHAR(100),
"update_time" VARCHAR(100),
NOT CLUSTER PRIMARY KEY("result_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS IS '考核成绩表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."assessment_date" IS '考核日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."assessment_id" IS '考核项目编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."assessment_time" IS '考核时间段(上午/下午/晚上)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."pass_status" IS '通过状态(通过/不通过)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."result_id" IS '编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."score" IS '考核分数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."update_time" IS '修改时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ASSESSMENT_RESULTS."ygxx_id" IS '人员编号';


CREATE OR REPLACE  INDEX "JIGEXIONGDI"."idx_personnel_date" ON "JIGEXIONGDI"."WJDXYY_ASSESSMENT_RESULTS"("ygxx_id" ASC,"assessment_date" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "JIGEXIONGDI"."idx_assessment_date" ON "JIGEXIONGDI"."WJDXYY_ASSESSMENT_RESULTS"("assessment_date" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "JIGEXIONGDI"."WJDXYY_BDKP"
(
"bdkp_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"bdkp_date" VARCHAR(50),
"bdkp_level" INT,
"bdkp_result" VARCHAR(50),
"bdkp_dzzjs" INT,
"bdkp_zbgz" INT,
"bdkp_jsxl" INT,
"bdkp_sxzz" INT,
"bdkp_rcgz" INT,
"bdkp_remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("bdkp_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_BDKP IS '部队考评表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_date" IS '考评时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_dzzjs" IS '党组织建设考核 1.好 2.中 3.一般';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_id" IS '主键,部队考评编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_jsxl" IS '军事训练考核 1.好 2.中 3.一般';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_level" IS '考评级别 1.五好单位 2.四好单位 3.三好单位 4.二好单位 5.一好单位 6.零好单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_rcgz" IS '日常工作考核 1.好 2.中 3.一般';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_remark" IS '考评备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_result" IS '考核结果详情';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_sxzz" IS '思想政治工作考核 1.好 2.中 3.一般';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."bdkp_zbgz" IS '战备工作考核 1.好 2.中 3.一般';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDKP."unit_id" IS '部队编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_BDRY"
(
"bdry_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"bdry_rylx" INT,
"bdkp_hqsj" VARCHAR(50),
"bdkp_remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("bdry_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_BDRY IS '部队集体荣誉表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDRY."bdkp_hqsj" IS '荣誉获取时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDRY."bdkp_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDRY."bdry_id" IS '主键,部队荣誉编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDRY."bdry_rylx" IS '荣誉类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_BDRY."unit_id" IS '部队编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_DWXC"
(
"dwxc_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"dwxc_type" INT,
"dwxc_date" VARCHAR(50),
"remark" VARCHAR(200),
CLUSTER PRIMARY KEY("dwxc_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_DWXC IS '单位宣传表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXC."dwxc_date" IS '宣传时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXC."dwxc_id" IS '主键，单位宣传编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXC."dwxc_type" IS '宣传类型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXC."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXC."unit_id" IS '所在单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_DWXX"
(
"dw_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"dw_uname" VARCHAR(50),
"dw_ubirth" VARCHAR(50),
"dw_idcard" VARCHAR(50),
"dw_uedu" INTEGER DEFAULT 0,
"dw_udjob" INT,
"dw_beginTime" VARCHAR(50),
"dw_endTime" VARCHAR(50),
"dw_gender" VARCHAR(50),
"jr_code" VARCHAR(50),
CLUSTER PRIMARY KEY("dw_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_DWXX IS '党委信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_beginTime" IS '上任时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_endTime" IS '离任时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_idcard" IS '身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_udjob" IS '党委类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."dw_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DWXX."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_DXPY"
(
"dxpy_id" INT IDENTITY(1, 1) NOT NULL,
"dxpy_name" VARCHAR(50),
"jr_code" VARCHAR(50),
"unit_id" VARCHAR(50),
"dxpy_phone" VARCHAR(50),
"dxpy_email" VARCHAR(50),
"dxpy_birth" VARCHAR(50),
"dxpy_nation" INT,
"dxpy_edu" INT,
"dxpy_politicalstatus" INT,
"dxpy_type" INT,
"dxpy_details" VARCHAR(50),
"dxpy_starttime" VARCHAR(50),
"dxpy_endtime" VARCHAR(50),
"dxpy_remark" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("dxpy_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_DXPY IS '典型培育表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_birth" IS '生日';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_edu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_email" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_endtime" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_id" IS '编号，主键，自增';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_name" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_nation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_phone" IS '手机号码';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_politicalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_starttime" IS '开始时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."dxpy_type" IS '培育详情';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DXPY."unit_id" IS '所在单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_DYCY"
(
"dycy_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"dycy_uname" VARCHAR(50),
"dycy_ubirth" VARCHAR(50),
"dycy_uedu" INTEGER DEFAULT 0,
"dycy_unation" INT,
"dycy_upoliticalstatus" INT,
"dycy_uaddr" VARCHAR(200),
"dycy_uemail" VARCHAR(50),
"dycy_upic" VARCHAR(50),
"dycy_uindate" VARCHAR(50),
"dycy_udjob" INT,
"dycy_ucoretype" INT DEFAULT 0,
"dycy_uissupervisor" INT,
"dycy_ujawyhjob" INT,
"dycy_utjob" INT,
"dycy_ustate" VARCHAR(50),
"dycy_coach" INT,
"dycy_gender" VARCHAR(50),
"dycy_starttime" VARCHAR(50),
"dycy_endtime" VARCHAR(50),
"jr_code" VARCHAR(50),
CLUSTER PRIMARY KEY("dycy_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_DYCY IS '基层部队党委信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_coach" IS '是否为教练员 0 否 1 是';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_endtime" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_starttime" IS '入党时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_uaddr" IS '家庭住址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_ucoretype" IS '骨干类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_udjob" IS '党委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_uemail" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_uindate" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_uissupervisor" IS '是否是风气监督员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_ujawyhjob" IS '军人委员会职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_unation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_upic" IS '照片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_upoliticalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_ustate" IS '状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."dycy_utjob" IS '团委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYCY."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_DYFZ"
(
"DYFZ_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"DYFZ_uname" VARCHAR(50),
"DYFZ_ubirth" VARCHAR(50),
"DYFZ_idcard" VARCHAR(50),
"DYFZ_uedu" INTEGER DEFAULT 0,
"DYFZ_fzlx" INT,
"DYFZ_beginTime" VARCHAR(50),
"DYFZ_endTime" VARCHAR(50),
"DYFZ_gender" VARCHAR(50),
"jr_code" VARCHAR(50),
CLUSTER PRIMARY KEY("DYFZ_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_DYFZ IS '党员发展信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_beginTime" IS '发展时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_endTime" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_fzlx" IS '发展类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_idcard" IS '身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."DYFZ_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_DYFZ."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ENVIRONMENT_FACTORS"
(
"env_id" INT IDENTITY(1, 1) NOT NULL,
"assessment_date" VARCHAR(50),
"unit_id" VARCHAR(50),
"weather_condition" VARCHAR(50),
"temperature" DECIMAL(5,2),
"humidity" DECIMAL(5,2),
"training_ground_condition" VARCHAR(50),
"special_conditions" VARCHAR(200),
"create_time" VARCHAR(50),
"update_time" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("env_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS IS '环境因素表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."assessment_date" IS '考核日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."env_id" IS '编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."humidity" IS '湿度(%)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."special_conditions" IS '特殊环境条件';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."temperature" IS '温度(℃)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."training_ground_condition" IS '场地状况';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."update_time" IS '修改时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ENVIRONMENT_FACTORS."weather_condition" IS '天气状况';


CREATE OR REPLACE  INDEX "JIGEXIONGDI"."idx_unit_date" ON "JIGEXIONGDI"."WJDXYY_ENVIRONMENT_FACTORS"("unit_id" ASC,"assessment_date" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "JIGEXIONGDI"."WJDXYY_EQUIPMENT_SUPPORT"
(
"equip_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" VARCHAR(50) NOT NULL,
"assessment_date" VARCHAR(50) NOT NULL,
"equipment_integrity_rate" DECIMAL(5,2),
"equipment_satisfaction" INT,
"equipment_issues" VARCHAR(200),
"create_time" VARCHAR(100),
"update_time" VARCHAR(100),
NOT CLUSTER PRIMARY KEY("equip_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT IS '装备保障表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."assessment_date" IS '考核日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."equip_id" IS '编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."equipment_integrity_rate" IS '装备完好率(%)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."equipment_issues" IS '装备问题记录';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."equipment_satisfaction" IS '装备满意度(1-5)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_EQUIPMENT_SUPPORT."update_time" IS '修改时间';


CREATE OR REPLACE  INDEX "JIGEXIONGDI"."idx_unit_date_equip" ON "JIGEXIONGDI"."WJDXYY_EQUIPMENT_SUPPORT"("unit_id" ASC,"assessment_date" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "JIGEXIONGDI"."WJDXYY_FACTOR_ANALYSIS_RESULTS"
(
"analysis_id" INT IDENTITY(1, 1) NOT NULL,
"factor_category" VARCHAR(50) NOT NULL,
"factor_name" VARCHAR(100) NOT NULL,
"correlation_coefficient" DECIMAL(5,4),
"significance_level" DECIMAL(5,4),
"impact_weight" DECIMAL(5,4),
"analysis_date" VARCHAR(50) NOT NULL,
"analyst" VARCHAR(50),
"create_time" VARCHAR(100),
"update_time" VARCHAR(100),
"gj_remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("analysis_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS IS '影响因素分析结果表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."analysis_date" IS '分析日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."analysis_id" IS '编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."analyst" IS '分析人员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."correlation_coefficient" IS '与成绩的相关系数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."factor_category" IS '因素类别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."factor_name" IS '具体因素名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."gj_remark" IS '告警描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."impact_weight" IS '影响权重';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."significance_level" IS '显著性水平';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FACTOR_ANALYSIS_RESULTS."update_time" IS '修改时间';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_FQJDY"
(
"fqjdy_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"fqjdy_uname" VARCHAR(50),
"fqjdy_ubirth" VARCHAR(50),
"fqjdy_uedu" INTEGER DEFAULT 0,
"fqjdy_unation" INT,
"fqjdy_upoliticalstatus" INT,
"fqjdy_uaddr" VARCHAR(200),
"fqjdy_uemail" VARCHAR(50),
"fqjdy_upic" VARCHAR(50),
"fqjdy_uindate" VARCHAR(50),
"fqjdy_udjob" INT,
"fqjdy_ucoretype" INT DEFAULT 0,
"fqjdy_uissupervisor" INT,
"fqjdy_ujawyhjob" INT,
"fqjdy_utjob" INT,
"fqjdy_ustate" VARCHAR(50),
"fqjdy_coach" INT,
"fqjdy_gender" VARCHAR(50),
"fqjdy_starttime" VARCHAR(50),
"fqjdy_endtime" VARCHAR(50),
"jr_code" VARCHAR(50),
CLUSTER PRIMARY KEY("fqjdy_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_FQJDY IS '基层部队风气监督员信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_coach" IS '是否为教练员 0 否 1 是';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_endtime" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_starttime" IS '成为风气监督员时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_uaddr" IS '家庭住址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_ucoretype" IS '骨干类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_udjob" IS '党委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_uemail" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_uindate" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_uissupervisor" IS '是否是风气监督员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_ujawyhjob" IS '军人委员会职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_unation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_upic" IS '照片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_upoliticalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_ustate" IS '状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."fqjdy_utjob" IS '团委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FQJDY."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_FXGJ"
(
"fxgj_id" INT IDENTITY(1, 1) NOT NULL,
"fxgj_name" VARCHAR(50),
"unit_id" INT,
"fxgj_type" INT,
"fxgj_details" VARCHAR(50),
"fxgj_fxzt" INT,
"fxgj_creattime" VARCHAR(50),
"fxgj_fxtime" VARCHAR(50),
"fxgj_gjjy" VARCHAR(50),
"fxgj_fxyj" VARCHAR(50),
"fxgj_cnzt" INT,
"fxgj_remark" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("fxgj_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_FXGJ IS '问题分析和改进建议';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_cnzt" IS '采纳状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_creattime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_details" IS '问题描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_fxtime" IS '分析时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_fxyj" IS '分析依据';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_fxzt" IS '分析状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_gjjy" IS '改进建议';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_id" IS '编号，主键，自增';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_name" IS '预警名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."fxgj_type" IS '预警类别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXGJ."unit_id" IS '问题单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_FXJL"
(
"jlid" INT IDENTITY(1, 1) NOT NULL,
"jltscId" INT,
"tscnr" VARCHAR(2000),
"tsclx" INT,
"fxjgnr" VARCHAR(2000),
"fxgl" VARCHAR(50),
"fxsj" DATETIME(6),
NOT CLUSTER PRIMARY KEY("jlid")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_FXJL IS '综合分析记录表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXJL."fxgl" IS '分析关联平衡度分析 关系分析模型编号  趋势分析传唯一名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXJL."fxjgnr" IS '分析结果内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXJL."fxsj" IS '分析时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXJL."jlid" IS '记录编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXJL."jltscId" IS '提示词编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXJL."tsclx" IS '提示词类型1.平衡度  2.趋势';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXJL."tscnr" IS '提示词内容';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_FXMX"
(
"fxmx_id" INT IDENTITY(1, 1) NOT NULL,
"fxmx_name" VARCHAR(50),
"fxmx_type" INT,
"fxmx_isfb" INT,
"fxmx_xs" FLOAT,
"fxmx_gz" VARCHAR(100),
"fxmx_createtime" VARCHAR(100),
"fxmx_remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("fxmx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_FXMX IS '分析模型表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_createtime" IS '模型创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_gz" IS '模型触发规则';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_id" IS '分析模型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_isfb" IS '模式是否发布';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_name" IS '分析模型名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_type" IS '分析模型类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXMX."fxmx_xs" IS '模型系数';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_FXTSC"
(
"tscid" INT IDENTITY(1, 1) NOT NULL,
"tscmc" VARCHAR(100),
"tscnr" VARCHAR(2000),
"tscfxlx" INT,
"createdTime" DATETIME(6),
"updatedTime" DATETIME(6),
"tsczt" INT DEFAULT 1,
NOT CLUSTER PRIMARY KEY("tscid")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_FXTSC IS '分析提示词';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXTSC."createdTime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXTSC."tscfxlx" IS '提示词类型[1.平衡度分析 2.趋势分析]';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXTSC."tscid" IS '主键编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXTSC."tscmc" IS '提示词名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXTSC."tscnr" IS '提示词内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXTSC."tsczt" IS '状态（1,启用  0 禁用）';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXTSC."updatedTime" IS '更新时间';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_FXWT"
(
"fxwt_id" INT IDENTITY(1, 1) NOT NULL,
"yjgl_id" INT,
"yjnr_id" INT,
"yjgl_type" INT,
"fxtime" VARCHAR(50),
"createtime" VARCHAR(50),
"fxyj" VARCHAR(2000),
"wtms" VARCHAR(2000),
"unit_id" INT,
"unit_name" VARCHAR(50),
"gjyj" VARCHAR(2000),
"fxzt" INT,
"cnzt" INT,
"cljy" VARCHAR(2000),
"yjmc" VARCHAR(500),
"yjlbmc" VARCHAR(500),
"yjnrmc" VARCHAR(500),
"clzt" INT,
NOT CLUSTER PRIMARY KEY("fxwt_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_FXWT IS '分析问题表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."cljy" IS '处理建议';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."clzt" IS '处理状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."cnzt" IS '采纳状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."createtime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."fxtime" IS '分析时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."fxyj" IS '分析依据';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."fxzt" IS '分析状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."gjyj" IS '改建意见';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."unit_id" IS '问题单位id';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."unit_name" IS '问题单位名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."wtms" IS '问题描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."yjgl_id" IS '预警管理ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."yjgl_type" IS '预警类别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."yjlbmc" IS '预警类别名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."yjmc" IS '预警名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."yjnr_id" IS '预警问题ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_FXWT."yjnrmc" IS '预警内容名称';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_GBXL"
(
"gbxl_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"gbxl_xllx" INT,
"gbxl_xlcj" INT,
"gbxl_begin_time" VARCHAR(50),
"gbxl_end_time" VARCHAR(50),
"gbxl_xlsc" INT,
"gbxl_khr" INT,
"gbxl_remark" VARCHAR(50),
"jsxl_id" INT,
"gbxl_ck" INT,
"gbxl_tx" INT,
NOT CLUSTER PRIMARY KEY("gbxl_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_GBXL IS '官兵训练表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_begin_time" IS '训练开始时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_ck" IS '是否重考';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_end_time" IS '训练结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_khr" IS '考核人';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_tx" IS '是否退训';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_xlcj" IS '训练成绩';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_xllx" IS '训练类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."gbxl_xlsc" IS '训练时长';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."jsxl_id" IS '军事训练';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GBXL."ygxx_id" IS '官兵信息';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_GGCY"
(
"ygxx_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jr_code" VARCHAR(50),
"ygxx_uname" VARCHAR(50),
"ygxx_ubirth" VARCHAR(50),
"ygxx_uedu" INTEGER DEFAULT 0,
"ygxx_unation" INT,
"ygxx_upoliticalstatus" INT,
"ygxx_uaddr" VARCHAR(200),
"ygxx_uemail" VARCHAR(50),
"ygxx_upic" VARCHAR(50),
"ygxx_uindate" VARCHAR(50),
"ygxx_udjob" INT,
"ygxx_ucoretype" INT DEFAULT 0,
"ygxx_uissupervisor" INT,
"ygxx_ujawyhjob" INT,
"ygxx_utjob" INT,
"ygxx_ustate" VARCHAR(50),
"ygxx_coach" INT,
"ygxx_gender" VARCHAR(50),
"gg_begin_time" VARCHAR(50),
"gg_end_time" VARCHAR(50),
CLUSTER PRIMARY KEY("ygxx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_GGCY IS '基层部队骨干成员信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."gg_begin_time" IS '成为骨干时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."gg_end_time" IS '取消骨干时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_coach" IS '是否为教练员 0 否 1 是';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_uaddr" IS '家庭住址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_ucoretype" IS '骨干类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_udjob" IS '党委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_uemail" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_uindate" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_uissupervisor" IS '是否是风气监督员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_ujawyhjob" IS '军人委员会职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_unation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_upic" IS '照片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_upoliticalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_ustate" IS '状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGCY."ygxx_utjob" IS '团委职务';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_GGJX"
(
"ggjx_id" INT IDENTITY(1, 1) NOT NULL,
"ggjx_pxzt" VARCHAR(50),
"ggjx_kzsj" VARCHAR(50),
"ggjx_dd" VARCHAR(50),
"ggjx_fs" VARCHAR(50),
"unit_id" INT,
CLUSTER PRIMARY KEY("ggjx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_GGJX IS '骨干集训表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGJX."ggjx_dd" IS '地点';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGJX."ggjx_fs" IS '方式';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGJX."ggjx_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGJX."ggjx_kzsj" IS '开展时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGJX."ggjx_pxzt" IS '培训主题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GGJX."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_GJCTJ"
(
"gjctj_id" INT IDENTITY(1, 1) NOT NULL,
"gjctj_gjc" VARCHAR(50),
"yxys_lx" INT,
"gjctj_js" INT,
NOT CLUSTER PRIMARY KEY("gjctj_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_GJCTJ IS '影响因素关键词统计表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GJCTJ."gjctj_gjc" IS '关键词';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GJCTJ."gjctj_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GJCTJ."gjctj_js" IS '件数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GJCTJ."yxys_lx" IS '影响因素类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_GZDX"
(
"gzdx_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"gzdx_wt" VARCHAR(100),
"gzdx_cs" VARCHAR(200),
"gzdx_remark" VARCHAR(200),
"JR_CODE" VARCHAR(50),
"create_time" VARCHAR(100),
CLUSTER PRIMARY KEY("gzdx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_GZDX IS '重点关注对象表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."gzdx_cs" IS '关注措施';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."gzdx_id" IS '主键，关注对象编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."gzdx_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."gzdx_wt" IS '关注对象问题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_GZDX."ygxx_id" IS '关注对象编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_HDKZNR"
(
"hdkz_id" INT IDENTITY(1, 1) NOT NULL,
"yxys_id" INT,
"hdkz_nr" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("hdkz_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_HDKZNR IS '影响因素挖掘分析活动开展内容表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_HDKZNR."hdkz_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_HDKZNR."hdkz_nr" IS '活动开展内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_HDKZNR."yxys_id" IS '影响因素';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_HQBZ"
(
"hqbz_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"hqbz_whlx" INT,
"hqbz_remark" VARCHAR(1000),
CLUSTER PRIMARY KEY("hqbz_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_HQBZ IS '后勤保障';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_HQBZ."hqbz_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_HQBZ."hqbz_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_HQBZ."hqbz_whlx" IS '维护类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_HQBZ."unit_id" IS '所在单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JDJC"
(
"jdjc_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jdjc_type" INT,
"jdjc_date" VARCHAR(50),
"jdjc_number" INT,
"jdjc_details" VARCHAR(200),
"jdjc_iswgwj" INT,
"jdjc_remark" VARCHAR(200),
CLUSTER PRIMARY KEY("jdjc_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JDJC IS '监督检查表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."jdjc_date" IS '检查时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."jdjc_details" IS '检查详情';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."jdjc_id" IS '监督检查编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."jdjc_iswgwj" IS '是否违规违纪';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."jdjc_number" IS '检查人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."jdjc_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."jdjc_type" IS '监督检查类型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JDJC."unit_id" IS '所在单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JLDK"
(
"jldk_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"dk_type" INT,
"dk_date" VARCHAR(50),
"dk_ydnum" INT,
"dk_sdnum" INT,
"dk_qqlx" INT,
"dk_details" VARCHAR(200),
"dk_remark" VARCHAR(100),
CLUSTER PRIMARY KEY("jldk_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JLDK IS '纪律党课';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."dk_date" IS '上课时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."dk_details" IS '上课详情';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."dk_qqlx" IS '缺勤类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."dk_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."dk_sdnum" IS '实到人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."dk_type" IS '党课类型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."dk_ydnum" IS '应到人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."jldk_id" IS '纪律党课编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLDK."unit_id" IS '所在单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JLY"
(
"jly_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jly_uname" VARCHAR(50),
"jly_ubirth" VARCHAR(50),
"jly_idcard" VARCHAR(50),
"jly_uedu" INTEGER DEFAULT 0,
"jly_rylx" INTEGER DEFAULT 0,
"jly_beginTime" VARCHAR(50),
"jly_endTime" VARCHAR(50),
"jly_gender" VARCHAR(50),
"jr_code" VARCHAR(50),
"jly_yx" INT DEFAULT 0,
CLUSTER PRIMARY KEY("jly_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JLY IS '教练员信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_beginTime" IS '教练员时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_endTime" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_idcard" IS '身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_rylx" IS '荣誉类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jly_yx" IS '是否为优秀教练员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLY."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JLYPF"
(
"jlypf_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"jlypf_date" VARCHAR(50),
"jlypf_pf" DOUBLE,
"jlypf_remark" VARCHAR(50),
"jsxl_id" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("jlypf_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JLYPF IS '教练员评分表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLYPF."jlypf_date" IS '评分时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLYPF."jlypf_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLYPF."jlypf_pf" IS '教练员评分';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLYPF."jlypf_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLYPF."unit_id" IS '所在部门';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JLYPF."ygxx_id" IS '教练员信息';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JRWYH"
(
"jrwyh_id" INT IDENTITY(1, 1) NOT NULL,
"jrwyh_name" VARCHAR(50),
"jr_code" VARCHAR(50),
"unit_id" VARCHAR(50),
"jrwyh_phone" VARCHAR(50),
"jrwyh_email" VARCHAR(50),
"jrwyh_birth" VARCHAR(50),
"jrwyh_nation" INT,
"jrwyh_edu" INT,
"jrwyh_zw" INT,
"jrwyh_starttime" VARCHAR(50),
"jrwyh_endtime" VARCHAR(50),
"jrwyh_remark" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("jrwyh_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JRWYH IS '军人委员会';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_birth" IS '生日';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_edu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_email" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_endtime" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_id" IS '编号，主键，自增';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_name" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_nation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_phone" IS '手机号码';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_starttime" IS '开始时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."jrwyh_zw" IS '职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JRWYH."unit_id" IS '所在单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JSXL"
(
"jsxl_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jsxl_jly" INT,
"jsxl_rs" INT,
"jsxl_xlcd" INT,
"jsxl_date" VARCHAR2(50),
"jsxl_xllx" INT,
"jsxl_sdrs" INT,
"jsxl_cql" NUMBER(5,2),
"jsxl_jhsc" INT,
CLUSTER PRIMARY KEY("jsxl_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JSXL IS '军事训练记录表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_cql" IS '训练出勤率';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_date" IS '训练时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_id" IS '军事训练记录编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_jhsc" IS '计划训练时长';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_jly" IS '教练员编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_rs" IS '训练人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_sdrs" IS '训练实到人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_xlcd" IS '训练场地编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."jsxl_xllx" IS '训练类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JSXL."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JTGJ"
(
"jtgj_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"jsy_id" INT,
"jtgj_sysy" VARCHAR(50),
"jtgj_csd" VARCHAR(50),
"jtgj_mdd" VARCHAR(50),
"jtgj_clwz" VARCHAR(50),
"jtgj_sysj" VARCHAR(50),
"jtgj_ghsj" VARCHAR(50),
"jtgj_yslc" INT,
"jtgl_remark" VARCHAR(50),
CLUSTER PRIMARY KEY("jtgj_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JTGJ IS '交通工具使用情况表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jsy_id" IS '驾驶员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_clwz" IS '车辆违章';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_csd" IS '初始地';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_ghsj" IS '车辆归还时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_mdd" IS '目的地';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_sysj" IS '车辆使用时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_sysy" IS '使用事由';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgj_yslc" IS '车辆运输里程';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."jtgl_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JTGJ."ygxx_id" IS '车辆使用人';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_JWHD"
(
"jwhd_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jwhd_title" VARCHAR(100),
"jwhd_addr" VARCHAR(100),
"jwhd_date" VARCHAR(50),
"jwhd_remark" VARCHAR(200),
CLUSTER PRIMARY KEY("jwhd_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_JWHD IS '军人委员会活动表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JWHD."jwhd_addr" IS '活动开展地点';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JWHD."jwhd_date" IS '活动开展时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JWHD."jwhd_id" IS '主键，活动编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JWHD."jwhd_remark" IS '活动备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JWHD."jwhd_title" IS '活动开展主题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_JWHD."unit_id" IS '活动单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_KHCJ"
(
"khcj_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"khcj_khsj" VARCHAR(50),
"khcj_kmlx" INT,
"khcj_khcj" INT,
"khcj_remark" VARCHAR(50),
"JR_CODE" VARCHAR(50),
CLUSTER PRIMARY KEY("khcj_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_KHCJ IS '考核表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."khcj_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."khcj_khcj" IS '考核成绩';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."khcj_khsj" IS '考核时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."khcj_kmlx" IS '考核科目类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."khcj_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KHCJ."ygxx_id" IS '人员编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_KXTG"
(
"kxtg_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"kxtg_qk" VARCHAR(200),
"kxtg_remark" VARCHAR(200),
"JR_CODE" VARCHAR(50),
CLUSTER PRIMARY KEY("kxtg_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_KXTG IS '考学提干表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KXTG."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KXTG."kxtg_id" IS '主键，考学提干编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KXTG."kxtg_qk" IS '考学提干情况';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KXTG."kxtg_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KXTG."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_KXTG."ygxx_id" IS '士兵编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_MEETING"
(
"meeting_id" INT IDENTITY(1, 1) NOT NULL,
"meeting_title" VARCHAR(200),
"meeting_type" INT,
"meeting_time" VARCHAR(100),
"meeting_status" INT,
"remark" VARCHAR(300),
NOT CLUSTER PRIMARY KEY("meeting_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_MEETING IS '会议记录表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING."meeting_id" IS '会议编号,主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING."meeting_status" IS '会议状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING."meeting_time" IS '会议时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING."meeting_title" IS '会议主题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING."meeting_type" IS '会议类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING."remark" IS '备注';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_MEETING_DETAIL"
(
"meeting_detail_id" INT IDENTITY(1, 1) NOT NULL,
"meeting_id" INT,
"meeting_content" VARCHAR(400),
"meeting_record" VARCHAR(200),
"meeting_chry" VARCHAR(100),
"remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("meeting_detail_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_MEETING_DETAIL IS '会议详情表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING_DETAIL."meeting_chry" IS '参会人员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING_DETAIL."meeting_content" IS '会议摘要';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING_DETAIL."meeting_detail_id" IS '会议详情编号,主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING_DETAIL."meeting_id" IS '会议编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING_DETAIL."meeting_record" IS '会议记录';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MEETING_DETAIL."remark" IS '备注';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_MXDX"
(
"mxdx_id" INT IDENTITY(1, 1) NOT NULL,
"mxdx_name" VARCHAR(50),
"mxdx_table_name" VARCHAR(100),
"mxdx_column_name" VARCHAR(100),
"mxdx_createtime" VARCHAR(100),
"mxdx_value" VARCHAR(100),
"mxdx_remark" VARCHAR(200),
"mxdx_type" INT,
"dict_type" VARCHAR(100),
NOT CLUSTER PRIMARY KEY("mxdx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_MXDX IS '分析模型对象表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."dict_type" IS '模型分析对象关联字典类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_column_name" IS '模型对象关联数据库表字段名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_createtime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_id" IS '分析模型对象编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_name" IS '模型分析对象名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_table_name" IS '模型对象关联数据库表表名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_type" IS '模型类型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXDX."mxdx_value" IS '分析对象数值';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_MXGZ"
(
"mxgz_id" INT IDENTITY(1, 1) NOT NULL,
"mxdx_id" INT,
"mxgz_dl" VARCHAR(50),
"mxgz_gz" VARCHAR(100),
"mxgz_yz" FLOAT,
"mxgz_createtime" VARCHAR(100),
"mxgz_remark" VARCHAR(200),
"dict_type" VARCHAR(50),
"dict_value" VARCHAR(50),
"fxmx_id" INT,
NOT CLUSTER PRIMARY KEY("mxgz_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_MXGZ IS '模型规则表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."dict_type" IS '关联字典类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."dict_value" IS '关联字典值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."fxmx_id" IS '分析模型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."mxdx_id" IS '规则分析对象编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."mxgz_createtime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."mxgz_dl" IS '模型规则度量';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."mxgz_gz" IS '模型规则度量规则';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."mxgz_id" IS '模型规则编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."mxgz_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXGZ."mxgz_yz" IS '模型规则度量阈值';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_MXLX"
(
"mxlx_id" INT IDENTITY(1, 1) NOT NULL,
"mxlx_name" VARCHAR(100),
"mxlx_createtime" VARCHAR(100),
"mxlx_remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("mxlx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_MXLX IS '分析模型类型表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXLX."mxlx_createtime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXLX."mxlx_id" IS '模型类型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXLX."mxlx_name" IS '模型类型名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_MXLX."mxlx_remark" IS '备注';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS"
(
"factor_id" INT IDENTITY(1, 1) NOT NULL,
"ygxx_id" VARCHAR(50) NOT NULL,
"assessment_date" VARCHAR(50) NOT NULL,
"sleep_hours" DECIMAL(4,2),
"meal_satisfaction" INT,
"fatigue_level" INT,
"stress_level" INT,
"preparation_level" INT,
"mental_state" VARCHAR(50),
"create_time" VARCHAR(100),
"update_time" VARCHAR(100),
NOT CLUSTER PRIMARY KEY("factor_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS IS '生理心理因素表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."assessment_date" IS '考核日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."factor_id" IS '编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."fatigue_level" IS '疲劳程度(1-5)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."meal_satisfaction" IS '饮食满意度(1-5)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."mental_state" IS '心理状态评估';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."preparation_level" IS '准备充分程度(1-5)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."sleep_hours" IS '前夜睡眠时间(小时)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."stress_level" IS '压力水平(1-5)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."update_time" IS '修改时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS."ygxx_id" IS '人员编号';


CREATE OR REPLACE  INDEX "JIGEXIONGDI"."idx_personnel_date_psy" ON "JIGEXIONGDI"."WJDXYY_PHYSIOLOGICAL_PSYCHOLOGICAL_FACTORS"("ygxx_id" ASC,"assessment_date" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "JIGEXIONGDI"."WJDXYY_QZXWHHD"
(
"qzxwhhd_id" INT IDENTITY(1, 1) NOT NULL,
"qzxwhhd_type" INT,
"qzxwhhd_date" VARCHAR(100),
"qzxwhhd_title" VARCHAR(100),
"qzxwhhd_content" VARCHAR(200),
"qzxwhhd_cyrs" INT,
"unit_id" INT,
"qzxwhhd_hjqk" INT,
"qzxwhhd_addr" VARCHAR(100),
"remark" VARCHAR(300),
NOT CLUSTER PRIMARY KEY("qzxwhhd_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_QZXWHHD IS '思想政治活动-群众性文化活动';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_addr" IS '活动地址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_content" IS '群众性文化活动内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_cyrs" IS '参与人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_date" IS '群众性文化活动时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_hjqk" IS '活动获奖情况';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_id" IS '群众性文化活动编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_title" IS '群众性文化活动主题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."qzxwhhd_type" IS '群众性文化活动类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_QZXWHHD."unit_id" IS '部门编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_RYBH"
(
"rybh_id" INT IDENTITY(1, 1) NOT NULL,
"rybh_bhlx" INT,
"rybh_ygxxId" INT,
"unit_id" INT,
"rybh_old" INT,
"rybh_new" INT,
"rybh_time" DATE,
"rybh_remark" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("rybh_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_RYBH IS '人员变化表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."rybh_bhlx" IS '变化类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."rybh_id" IS '变化编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."rybh_new" IS '变化新值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."rybh_old" IS '变化旧值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."rybh_remark" IS '变化备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."rybh_time" IS '变化时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."rybh_ygxxId" IS '变化人员编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYBH."unit_id" IS '变化人员所属部门';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_RYLD"
(
"ryld_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ryld_ldlx" INT,
"ryld_ldsy" VARCHAR(50),
"ryld_ldqx" VARCHAR(50),
"ryld_ldsj" VARCHAR(50),
"ryld_gdsj" VARCHAR(50),
"ryld_remark" VARCHAR(50),
"ygxx_id" INT,
"JR_CODE" VARCHAR(50),
"ryld_yjgdsj" VARCHAR(50),
CLUSTER PRIMARY KEY("ryld_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_RYLD IS '人员流动表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_gdsj" IS '归队时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_ldlx" IS '流动类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_ldqx" IS '流动去向';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_ldsj" IS '离队时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_ldsy" IS '流动事由';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ryld_yjgdsj" IS '预计归队时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLD."ygxx_id" IS '人员信息';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_RYLDYC"
(
"ryldyc_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" VARCHAR(50),
"ygxx_id" INT,
"ryldyc_ycsm" VARCHAR(50),
"ryldyc_sj" VARCHAR(50),
"ryldyc_remark" VARCHAR(50),
"JR_CODE" VARCHAR(50),
"ryldyc_yclx" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("ryldyc_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_RYLDYC IS '人员流动异常表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."ryldyc_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."ryldyc_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."ryldyc_sj" IS '发生时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."ryldyc_yclx" IS '异常类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."ryldyc_ycsm" IS '异常说明';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYLDYC."ygxx_id" IS '人员信息';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_RYRY"
(
"ryry_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"ryry_rylx" INT,
"ryry_hjsj" VARCHAR(50),
"ryry_remake" VARCHAR(50),
"JR_CODE" VARCHAR(50),
CLUSTER PRIMARY KEY("ryry_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_RYRY IS '人员荣誉';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYRY."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYRY."ryry_hjsj" IS '获奖时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYRY."ryry_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYRY."ryry_remake" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYRY."ryry_rylx" IS '荣誉类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYRY."unit_id" IS '所属单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_RYRY."ygxx_id" IS '员工编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_SBQK"
(
"sbqk_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"sbqk_sbrs" INT,
"sbqk_sblx" INT,
"sbqk_remark" VARCHAR(200),
"sbqk_sbsj" VARCHAR(50),
"sbqk_sbyy" VARCHAR(100),
CLUSTER PRIMARY KEY("sbqk_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_SBQK IS '军事训练伤病员情况表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBQK."sbqk_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBQK."sbqk_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBQK."sbqk_sblx" IS '伤病类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBQK."sbqk_sbrs" IS '伤病员人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBQK."sbqk_sbsj" IS '伤病发生时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBQK."sbqk_sbyy" IS '伤病发生原因';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBQK."unit_id" IS '所在单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_SBY"
(
"sby_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jr_code" VARCHAR(50),
"ygxx_uname" VARCHAR(50),
"ygxx_ubirth" VARCHAR(50),
"ygxx_gender" VARCHAR(50),
"ygxx_uaddr" VARCHAR(200),
"ygxx_uemail" VARCHAR(50),
"ygxx_upic" VARCHAR(50),
"ygxx_uindate" VARCHAR(50),
"sby_sblx" INT,
"sby_sbyy" VARCHAR(100),
"sb_begin_time" VARCHAR(50),
"sb_end_time" VARCHAR(50),
"sb_remark" VARCHAR(200),
CLUSTER PRIMARY KEY("sby_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_SBY IS '军事训练伤病员表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."sb_begin_time" IS '伤病发生时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."sb_end_time" IS '救治时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."sb_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."sby_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."sby_sblx" IS '伤病类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."sby_sbyy" IS '伤病发生原因';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."ygxx_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."ygxx_uaddr" IS '家庭住址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."ygxx_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."ygxx_uemail" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."ygxx_uindate" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."ygxx_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBY."ygxx_upic" IS '照片';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_SBYC"
(
"sbyc_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"sbyc_month" VARCHAR(50),
"sbyc_yfzd" VARCHAR(200),
"sbyc_jy" VARCHAR(200),
"sbyc_remark" VARCHAR(200),
"sbyc_zdgzdw" INT,
"sbyc_xysbyyz" INT,
NOT CLUSTER PRIMARY KEY("sbyc_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_SBYC IS '伤病预测表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."sbyc_id" IS '主键,伤病预测编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."sbyc_jy" IS '建议';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."sbyc_month" IS '伤病预测月份';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."sbyc_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."sbyc_xysbyyz" IS '下月伤病员预测数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."sbyc_yfzd" IS '预防重点';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."sbyc_zdgzdw" IS '重点关注单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SBYC."unit_id" IS '预测单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_SXZZ"
(
"sxzz_id" INTEGER IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"sxzz_type" VARCHAR(400),
"sxzz_time" VARCHAR(50),
"sxzz_hj" INT,
CLUSTER PRIMARY KEY("sxzz_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_SXZZ IS '思想政治活动表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SXZZ."sxzz_hj" IS '是否获奖（0表示未获奖，1表示获奖）';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SXZZ."sxzz_id" IS '活动ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SXZZ."sxzz_time" IS '活动时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SXZZ."sxzz_type" IS '活动类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SXZZ."unit_id" IS '所属单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_SYJZ"
(
"syjz_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"syjz_sj" VARCHAR(50),
"syjz_sq" VARCHAR(50),
"syjz_remark" VARCHAR(50),
"JR_CODE" VARCHAR(50),
CLUSTER PRIMARY KEY("syjz_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_SYJZ IS '伤员救治表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SYJZ."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SYJZ."syjz_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SYJZ."syjz_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SYJZ."syjz_sj" IS '伤员救治时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SYJZ."syjz_sq" IS '伤员伤情';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SYJZ."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_SYJZ."ygxx_id" IS '伤员';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_TBPP"
(
"tbpp_id" INT IDENTITY(1, 1) NOT NULL,
"tbpp_lx" INT,
"tbpp_time" VARCHAR(50),
"tbpp_remark" VARCHAR(500),
"unit_id" INT,
NOT CLUSTER PRIMARY KEY("tbpp_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_TBPP IS '通报批评';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TBPP."tbpp_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TBPP."tbpp_lx" IS '通报批评类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TBPP."tbpp_remark" IS '通报批评说明';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TBPP."tbpp_time" IS '通报批评时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TBPP."unit_id" IS '所在单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_TWHD"
(
"twhd_id" INT IDENTITY(1, 1) NOT NULL,
"twhd_title" VARCHAR(100),
"twhd_date" VARCHAR(50),
"unit_id" INT,
"twhd_addr" VARCHAR(50),
"twhd_remark" VARCHAR(200),
CLUSTER PRIMARY KEY("twhd_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_TWHD IS '团组织活动表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TWHD."twhd_addr" IS '活动开展地址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TWHD."twhd_date" IS '活动时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TWHD."twhd_id" IS '主键，团委活动编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TWHD."twhd_remark" IS '活动备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TWHD."twhd_title" IS '活动开展主题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TWHD."unit_id" IS '活动单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_TYCY"
(
"tycy_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"tycy_uname" VARCHAR(50),
"tycy_ubirth" VARCHAR(50),
"tycy_uedu" INTEGER DEFAULT 0,
"tycy_unation" INT,
"tycy_upoliticalstatus" INT,
"tycy_uaddr" VARCHAR(200),
"tycy_uemail" VARCHAR(50),
"tycy_upic" VARCHAR(50),
"tycy_uindate" VARCHAR(50),
"tycy_udjob" INT,
"tycy_ucoretype" INT DEFAULT 0,
"tycy_uissupervisor" INT,
"tycy_ujawyhjob" INT,
"tycy_utjob" INT,
"tycy_ustate" VARCHAR(50),
"tycy_coach" INT,
"tycy_gender" VARCHAR(50),
"tycy_starttime" VARCHAR(50),
"tycy_endtime" VARCHAR(50),
"jr_code" VARCHAR(50),
CLUSTER PRIMARY KEY("tycy_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_TYCY IS '基层部队团员信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_coach" IS '是否为教练员 0 否 1 是';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_endtime" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_starttime" IS '入团时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_uaddr" IS '家庭住址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_ucoretype" IS '骨干类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_udjob" IS '党委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_uemail" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_uindate" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_uissupervisor" IS '是否是风气监督员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_ujawyhjob" IS '军人委员会职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_unation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_upic" IS '照片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_upoliticalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_ustate" IS '状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."tycy_utjob" IS '团委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYCY."unit_id" IS '单位编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_TYRY"
(
"tyry_id" INT IDENTITY(1, 1) NOT NULL,
"tyry_name" VARCHAR(50),
"jr_code" VARCHAR(50),
"unit_id" VARCHAR(50),
"tyry_phone" VARCHAR(50),
"tyry_email" VARCHAR(50),
"tyry_birth" VARCHAR(50),
"tyry_nation" INT,
"tyry_edu" INT,
"tyry_pic" VARCHAR(50),
"tyry_politicalstatus" INT,
"tyry_uissupervisor" INT,
"tyry_ujawyhjob" INT,
"tyry_ucoretype" INT,
"tyry_utjob" INT,
"tyry_intime" VARCHAR(50),
"tyry_remark" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("tyry_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_TYRY IS '退役人员表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_birth" IS '生日';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_edu" IS '照片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_email" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_id" IS '编号，主键，自增';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_intime" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_name" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_nation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_phone" IS '手机号码';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_politicalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_ucoretype" IS '骨干类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_uissupervisor" IS '是否是风气监督员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_ujawyhjob" IS '军人委员会职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."tyry_utjob" IS '团委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_TYRY."unit_id" IS '所在单位';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_UNIT"
(
"unit_id" INT IDENTITY(1, 1) NOT NULL,
"unit_uname" VARCHAR(40),
"unit_addr" VARCHAR(200),
"unit_coordinate" VARCHAR(40),
"unit_introduction" VARCHAR(400),
NOT CLUSTER PRIMARY KEY("unit_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_UNIT IS '单位信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_UNIT."unit_addr" IS '单位地址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_UNIT."unit_coordinate" IS '单位坐标';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_UNIT."unit_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_UNIT."unit_introduction" IS '单位简介';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_UNIT."unit_uname" IS '单位名称';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_WDLX"
(
"wdlx_id" INT IDENTITY(1, 1) NOT NULL,
"wdlx_name" VARCHAR(200),
"create_time" VARCHAR(100),
"remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("wdlx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_WDLX IS '文档类型表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDLX."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDLX."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDLX."wdlx_id" IS '文档类型编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDLX."wdlx_name" IS '文档类型名称';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_WDXQ"
(
"wdxq_id" INT IDENTITY(1, 1) NOT NULL,
"wdxq_type" INT,
"mb_type" INT,
"wdxq_title" VARCHAR(200),
"wdxq_yq" VARCHAR(400),
"wdxq_content" VARCHAR(4000),
"wdxq_create_time" VARCHAR(50),
"wdxq_create_user" INT,
"remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("wdxq_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_WDXQ IS '文档详情表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."mb_type" IS '模板类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."wdxq_content" IS '文档内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."wdxq_create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."wdxq_create_user" IS '创建人';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."wdxq_id" IS '文档编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."wdxq_title" IS '文档标题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."wdxq_type" IS '文档类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WDXQ."wdxq_yq" IS '文档生成要求';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_WGWJ"
(
"wgwj_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jdjc_id" INT,
"wgwj_type" INT,
"wgwj_time" VARCHAR(50),
"wgwj_num" INT,
"wgwj_details" VARCHAR(200),
"wgwj_remark" VARCHAR(100),
CLUSTER PRIMARY KEY("wgwj_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_WGWJ IS '违规违纪表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."jdjc_id" IS '监督检查表编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."wgwj_details" IS '违规违纪详情';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."wgwj_id" IS '违规违纪编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."wgwj_num" IS '违规违纪人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."wgwj_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."wgwj_time" IS '违规违纪时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WGWJ."wgwj_type" IS '违规违纪类型编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_WSMB"
(
"wsmb_id" INT IDENTITY(1, 1) NOT NULL,
"wsmb_name" VARCHAR(100),
"wsmb_type" INT,
"wsmb_content" VARCHAR(2000),
"create_time" VARCHAR(200),
"remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("wsmb_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_WSMB IS '文书模版';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WSMB."create_time" IS '模板创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WSMB."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WSMB."wsmb_content" IS '模板内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WSMB."wsmb_id" IS '文书模板编号,主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WSMB."wsmb_name" IS '文书模板名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WSMB."wsmb_type" IS '模板类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_WTTZ"
(
"wttz_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"wttz_detail" VARCHAR(200),
"wttz_type" INT,
"wttz_wtsj" VARCHAR(50),
"wttz_remark" VARCHAR(200),
NOT CLUSTER PRIMARY KEY("wttz_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_WTTZ IS '基层部队问题台账表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WTTZ."unit_id" IS '部队编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WTTZ."wttz_detail" IS '问题描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WTTZ."wttz_id" IS '主键,问题台账编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WTTZ."wttz_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WTTZ."wttz_type" IS '问题类别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WTTZ."wttz_wtsj" IS '问题时间';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_WZGY"
(
"wzgy_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"wzgy_sj" VARCHAR(50),
"wzgy_sl" INT,
"wzgy_remark" VARCHAR(50),
CLUSTER PRIMARY KEY("wzgy_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_WZGY IS '军用物资供应表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WZGY."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WZGY."wzgy_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WZGY."wzgy_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WZGY."wzgy_sj" IS '物资供应时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_WZGY."wzgy_sl" IS '物资供应数量';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_XJJS"
(
"xjjs_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"xjjs_qk" VARCHAR(200),
"xjjs_remark" VARCHAR(200),
"JR_CODE" VARCHAR(50),
CLUSTER PRIMARY KEY("xjjs_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_XJJS IS '选晋警士表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XJJS."JR_CODE" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XJJS."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XJJS."xjjs_id" IS '主键，选晋警士编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XJJS."xjjs_qk" IS '关注对象情况';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XJJS."xjjs_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XJJS."ygxx_id" IS '士兵编号';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_XLCD"
(
"xlcd_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"xlcd_cdlx" INT DEFAULT 0,
"xlcd_nx" INT,
"xlcd_db" VARCHAR(50),
"xlcd_remark" VARCHAR(50),
CLUSTER PRIMARY KEY("xlcd_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_XLCD IS '训练场地表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLCD."unit_id" IS '所属单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLCD."xlcd_cdlx" IS '训练场地类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLCD."xlcd_db" IS '达标情况';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLCD."xlcd_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLCD."xlcd_nx" IS '使用年限';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLCD."xlcd_remark" IS '备注';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_XLFX"
(
"xlfx_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" VARCHAR(50),
"xlfx_fxlx" VARCHAR(50),
"xlfx_fxcs" INT,
"xlfx_remark" VARCHAR(50),
CLUSTER PRIMARY KEY("xlfx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_XLFX IS '军事训练风险情况表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLFX."unit_id" IS '所在单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLFX."xlfx_fxcs" IS '风险次数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLFX."xlfx_fxlx" IS '风险类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLFX."xlfx_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLFX."xlfx_remark" IS '备注';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_XLLX"
(
"xllx_id" INT IDENTITY(1, 1) NOT NULL,
"xllx_name" VARCHAR(50),
"xllx_parent_id" INT,
"xllx_leaf_node" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("xllx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_XLLX IS '训练类型表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLLX."xllx_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLLX."xllx_leaf_node" IS '是否为叶子节点';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLLX."xllx_name" IS '训练类型名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLLX."xllx_parent_id" IS '父一级训练类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_XLSB"
(
"xlsb_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_id" INT,
"xlsb_sblx" INT,
"xlsb_start_time" VARCHAR(50),
"xlsb_end_time" VARCHAR(50),
"remark" VARCHAR(500),
"jsxl_id" INT,
NOT CLUSTER PRIMARY KEY("xlsb_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_XLSB IS '训练伤病情况';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."jsxl_id" IS '军事训练';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."unit_id" IS '所在部门';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."xlsb_end_time" IS '伤病恢复时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."xlsb_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."xlsb_sblx" IS '伤病类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."xlsb_start_time" IS '伤病发生时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_XLSB."ygxx_id" IS '官兵信息';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YGXX"
(
"ygxx_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"ygxx_uname" VARCHAR(50),
"ygxx_ubirth" VARCHAR(50),
"ygxx_uedu" INTEGER DEFAULT 0,
"ygxx_unation" INT,
"ygxx_upoliticalstatus" INT,
"ygxx_uaddr" VARCHAR(200),
"ygxx_uemail" VARCHAR(50),
"ygxx_upic" VARCHAR(50),
"ygxx_uindate" VARCHAR(50),
"ygxx_udjob" INT,
"ygxx_ucoretype" INT DEFAULT 0,
"ygxx_uissupervisor" INT,
"ygxx_ujawyhjob" INT,
"ygxx_utjob" INT,
"ygxx_ustate" INT DEFAULT 1,
"ygxx_coach" INT,
"ygxx_gender" VARCHAR(50),
"ygxx_uoutdate" DATETIME(6),
"ygxx_zzmm" INT,
"ygxx_rank" INT,
"ygxx_major" INT,
"ygxx_four_have" INT,
"origin_region" VARCHAR(50),
"physical_condition" VARCHAR(50),
"service_years" INT,
CLUSTER PRIMARY KEY("ygxx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_YGXX IS '基层部队员工信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."origin_region" IS '籍贯地区(南方/北方)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."physical_condition" IS '基础体能状况评级';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."service_years" IS '服役年限';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_coach" IS '是否为教练员 0 否 1 是';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_four_have" IS '四有';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_major" IS '专业';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_rank" IS '职级';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_uaddr" IS '家庭住址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_ucoretype" IS '骨干类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_udjob" IS '党委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_uemail" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_uindate" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_uissupervisor" IS '是否是风气监督员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_ujawyhjob" IS '军人委员会职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_unation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_uoutdate" IS '退伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_upic" IS '照片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_upoliticalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_ustate" IS '状态(0.在役 1.退役)';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_utjob" IS '团委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YGXX."ygxx_zzmm" IS '政治面貌';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YJGL"
(
"yjgl_id" INT IDENTITY(1, 1) NOT NULL,
"yjgl_name" VARCHAR(50),
"unit_id" INT,
"yjgl_type" INT,
"yjgl_details" VARCHAR(50),
"yjgl_fbzt" INT,
"yjgl_dl" INT,
"yjgl_gz" INT,
"yjgl_yz" INT,
"yjgl_yjtl" VARCHAR(50),
"yjgl_creattime" VARCHAR(50),
"yjgl_clzt" INT,
"yjgl_cltime" VARCHAR(50),
"yjgl_remark" VARCHAR(50),
"yjgl_clyj" VARCHAR(500),
"yjnr_id" INT,
"yzlx" INT,
"yzjjx" INT,
NOT CLUSTER PRIMARY KEY("yjgl_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_YJGL IS '预警管理';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."unit_id" IS '问题单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_cltime" IS '处理时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_clyj" IS '处理意见';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_clzt" IS '处理状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_creattime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_details" IS '问题描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_dl" IS '度量';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_fbzt" IS '发布状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_gz" IS '规则';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_id" IS '编号，主键，自增';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_name" IS '预警名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_type" IS '预警类别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_yjtl" IS '预警通知';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjgl_yz" IS '阈值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yjnr_id" IS '预警内容ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yzjjx" IS '阈值警戒线';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL."yzlx" IS '阈值类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YJGL_RESULT"
(
"result_id" INT IDENTITY(1, 1) NOT NULL,
"parent_dept_id" NUMBER NOT NULL,
"dept_id" NUMBER NOT NULL,
"dept_name" VARCHAR2(100) NOT NULL,
"yjgl_type" NUMBER,
"yjnr_id" NUMBER,
"yjnr_name" VARCHAR2(200),
"abnormal_count" NUMBER DEFAULT 0,
"check_time" VARCHAR(50),
"status" NUMBER DEFAULT 0,
"create_time" VARCHAR(50),
"remark" VARCHAR2(500),
NOT CLUSTER PRIMARY KEY("result_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."abnormal_count" IS '异常数量';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."check_time" IS '检测时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."create_time" IS '创建时间（时间戳）';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."dept_id" IS '直接下级部门ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."dept_name" IS '部门名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."parent_dept_id" IS '父部门ID（执行检测的部门）';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."result_id" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."status" IS '状态：0-未处理，1-已处理';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."yjgl_type" IS '预警类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."yjnr_id" IS '预警内容ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT."yjnr_name" IS '预警内容名称';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YJGL_RESULT_DETAIL"
(
"detail_id" INT IDENTITY(1, 1) NOT NULL,
"result_id" NUMBER NOT NULL,
"dept_id" NUMBER NOT NULL,
"dept_name" VARCHAR2(100) NOT NULL,
"abnormal_data" VARCHAR2(2000),
"create_time" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("detail_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT_DETAIL."abnormal_data" IS '异常数据详情（JSON格式，包含具体的异常信息）';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT_DETAIL."create_time" IS '创建时间（时间戳）';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT_DETAIL."dept_id" IS '具体异常的部门ID（可能是下级部门的子部门）';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT_DETAIL."dept_name" IS '部门名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT_DETAIL."detail_id" IS '主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJGL_RESULT_DETAIL."result_id" IS '关联预警结果ID';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YJNR"
(
"yjnr_id" INT IDENTITY(1, 1) NOT NULL,
"yjnr_name" VARCHAR(200),
"yjnr_yjlxid" VARCHAR(50),
"yjnr_table_name" VARCHAR(50),
"yjnr_table_field" VARCHAR(50),
"yjnr_createtime" VARCHAR(50),
"yjnr_dict_type" VARCHAR(100),
"yjnr_dict_value" VARCHAR(100),
"yjnr_tishici" VARCHAR(2000),
"yjnr_fxbgmb" VARCHAR(2000),
"yjnr_table_field_value" VARCHAR(2000),
"yjnr_table_field_gz" VARCHAR(10),
NOT CLUSTER PRIMARY KEY("yjnr_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_YJNR IS '预警内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_createtime" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_dict_type" IS '字典类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_dict_value" IS '字典值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_fxbgmb" IS '分析报告模板';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_id" IS '预警内容表主键ID';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_name" IS '预警名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_table_field" IS '关联表字段';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_table_field_gz" IS '关联表字段规则';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_table_field_value" IS '关联表字段值';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_table_name" IS '关联表名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_tishici" IS '提示词';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YJNR."yjnr_yjlxid" IS '预警类型ID';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YXYS"
(
"YXYS_ID" INT IDENTITY(1, 1) NOT NULL,
"yxys_yxys" VARCHAR(50),
"yxys_gjms" VARCHAR(50),
"create_time" VARCHAR(50),
"end_time" VARCHAR(50),
"yxys_remark" VARCHAR(50),
"yxys_yslx" INT,
NOT CLUSTER PRIMARY KEY("YXYS_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_YXYS IS '影响因素挖掘分析指标表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYS."create_time" IS '创建时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYS."end_time" IS '结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYS."yxys_gjms" IS '告警描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYS."YXYS_ID" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYS."yxys_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYS."yxys_yslx" IS '因素类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYS."yxys_yxys" IS '影响因素';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YXYSCJ"
(
"yxyscj_id" INT IDENTITY(1, 1) NOT NULL,
"yxyscj_cjfs" VARCHAR(50),
"yxyscj_cjzt" VARCHAR(100),
"yxyscj_fxff" VARCHAR(50),
"yxyscj_cjdw" VARCHAR(50),
"yxyscj_cjsj" VARCHAR(50),
"yxyscj_cjnr" VARCHAR(200),
"yxyscj_remark" VARCHAR(50),
"yxyscj_yslx" INT,
NOT CLUSTER PRIMARY KEY("yxyscj_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_YXYSCJ IS '影响因素指标采集表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_cjdw" IS '采集单位';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_cjfs" IS '采集方式';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_cjnr" IS '采集内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_cjsj" IS '采集时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_cjzt" IS '采集主题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_fxff" IS '分析方法';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_id" IS '主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXYSCJ."yxyscj_yslx" IS '因素类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YXZZJY"
(
"ygxx_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"jr_code" VARCHAR(50),
"ygxx_uname" VARCHAR(50),
"ygxx_ubirth" VARCHAR(50),
"ygxx_uedu" INTEGER DEFAULT 0,
"ygxx_unation" INT,
"ygxx_upoliticalstatus" INT,
"ygxx_uaddr" VARCHAR(200),
"ygxx_uemail" VARCHAR(50),
"ygxx_upic" VARCHAR(50),
"ygxx_uindate" VARCHAR(50),
"ygxx_udjob" INT,
"ygxx_ucoretype" INT DEFAULT 0,
"ygxx_uissupervisor" INT,
"ygxx_ujawyhjob" INT,
"ygxx_utjob" INT,
"ygxx_ustate" VARCHAR(50),
"ygxx_coach" INT,
"ygxx_gender" VARCHAR(50),
"yxzzjy_begin_time" VARCHAR(50),
"yxzzjy_end_time" VARCHAR(50),
CLUSTER PRIMARY KEY("ygxx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_YXZZJY IS '基层部队优秀政治教员信息表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."jr_code" IS '军人身份证';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_coach" IS '是否为教练员 0 否 1 是';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_gender" IS '性别';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_id" IS '编号，主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_uaddr" IS '家庭住址';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_ubirth" IS '出生日期';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_ucoretype" IS '骨干类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_udjob" IS '党委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_uedu" IS '学历';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_uemail" IS '电子邮箱';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_uindate" IS '入伍时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_uissupervisor" IS '是否是风气监督员';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_ujawyhjob" IS '军人委员会职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_uname" IS '姓名';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_unation" IS '民族';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_upic" IS '照片';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_upoliticalstatus" IS '政治面貌';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_ustate" IS '状态';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."ygxx_utjob" IS '团委职务';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."yxzzjy_begin_time" IS '成为优秀政治教员时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YXZZJY."yxzzjy_end_time" IS '取消优秀政治教员时间';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_YZKPB"
(
"yzkpb_id" INT IDENTITY(1, 1) NOT NULL,
"yzkpb_type" INT,
"yzkpb_date" VARCHAR(100),
"yzkpb_rs" INT,
"yzkpb_score" INT,
"yzkpb_cyl" DOUBLE,
"unit_id" INT,
"remark" VARCHAR(300),
NOT CLUSTER PRIMARY KEY("yzkpb_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_YZKPB IS '思想政治活动-优质课评比';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."unit_id" IS '部门编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."yzkpb_cyl" IS '课程参与率';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."yzkpb_date" IS '课程时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."yzkpb_id" IS '优质课评比编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."yzkpb_rs" IS '课程参与人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."yzkpb_score" IS '课程评分';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_YZKPB."yzkpb_type" IS '课程类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ZBGZRW"
(
"zbgz_id" INTEGER IDENTITY(1, 1) NOT NULL,
"unit_id" INTEGER,
"zbgz_rwlx" VARCHAR(500),
"zbgz_date" VARCHAR(15),
"zbgz_rs" INTEGER,
"zbgz_zt" INTEGER,
"zbgz_rwxq" VARCHAR(2000),
"zbgz_addr" VARCHAR(50),
"zbgz_cj" INT,
NOT CLUSTER PRIMARY KEY("zbgz_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ZBGZRW IS '战备工作任务表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."unit_id" IS '基层部队编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_addr" IS '任务地点';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_cj" IS '战备任务成绩';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_date" IS '任务时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_id" IS '战备工作任务主键';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_rs" IS '任务人数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_rwlx" IS '任务类型';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_rwxq" IS '任务详情';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBGZRW."zbgz_zt" IS '任务状态';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ZBQK"
(
"zbqk_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"zbqk_whzb" INT,
"zbqk_yszb" INT,
"zbqk_zbwhl" INT,
"zbqk_remark" VARCHAR(200),
"create_time" VARCHAR(100),
CLUSTER PRIMARY KEY("zbqk_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ZBQK IS '装备情况表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBQK."create_time" IS '统计时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBQK."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBQK."zbqk_id" IS '主键，装备情况记录编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBQK."zbqk_remark" IS '装备情况备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBQK."zbqk_whzb" IS '完好装备数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBQK."zbqk_yszb" IS '有损装备数';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBQK."zbqk_zbwhl" IS '装备完好率百分比';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ZBWXBY"
(
"zbwxby_id" INT IDENTITY(1, 1) NOT NULL,
"zbwxby_desc" VARCHAR(1000),
"zbwxby_remark" VARCHAR(1000),
"zbwxby_date" VARCHAR(50),
"unit_id" INT,
CLUSTER PRIMARY KEY("zbwxby_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ZBWXBY IS '装备维修保养记录表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBWXBY."unit_id" IS '单位编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBWXBY."zbwxby_date" IS '装备维修保养时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBWXBY."zbwxby_desc" IS '装备维修保养描述';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBWXBY."zbwxby_id" IS '装备维修保养编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBWXBY."zbwxby_remark" IS '装备维修保养备注';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ZBYL"
(
"zbyl_id" INT IDENTITY(1, 1) NOT NULL,
"unit_id" INT,
"zbyl_title" VARCHAR(100),
"zbyl_content" VARCHAR(200),
"zbyl_starttime" VARCHAR(50),
"zbyl_endtime" VARCHAR(50),
"zbyl_type" INT,
"zbyl_remark" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("zbyl_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ZBYL IS '战备演练';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."unit_id" IS '部队编号';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."zbyl_content" IS '演练内容';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."zbyl_endtime" IS '演练结束时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."zbyl_id" IS '主键,战备演练';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."zbyl_remark" IS '备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."zbyl_starttime" IS '演练开始时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."zbyl_title" IS '演练标题';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZBYL."zbyl_type" IS '演练类型 1.综合演练 2.专项演练';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ZLGL"
(
"zlgl_id" INT IDENTITY(1, 1) NOT NULL,
"zlgl_name" VARCHAR(50),
"zlgl_path" VARCHAR(200),
"zlgl_gs" VARCHAR(20),
"zlgl_size" BIGINT,
"zlgl_scz" VARCHAR(50),
"zlgl_uptime" DATETIME(6),
"zlklx_id" INT,
NOT CLUSTER PRIMARY KEY("zlgl_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ZLGL IS '资料管理表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlgl_gs" IS '文件格式';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlgl_id" IS '编号，主键，自增';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlgl_name" IS '文件名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlgl_path" IS '文件路径';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlgl_scz" IS '上传者';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlgl_size" IS '文件大小';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlgl_uptime" IS '上传时间';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLGL."zlklx_id" IS '资料库类型';


CREATE TABLE "JIGEXIONGDI"."WJDXYY_ZLKLX"
(
"zlklx_id" INT IDENTITY(1, 1) NOT NULL,
"zlk_mc" VARCHAR(50),
"zlk_jj" VARCHAR(200),
"zlk_bz" VARCHAR(1000),
NOT CLUSTER PRIMARY KEY("zlklx_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE JIGEXIONGDI.WJDXYY_ZLKLX IS '资料库类型表';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLKLX."zlk_bz" IS '资料库备注';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLKLX."zlk_jj" IS '资料库简介';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLKLX."zlk_mc" IS '资料库名称';
COMMENT ON COLUMN JIGEXIONGDI.WJDXYY_ZLKLX."zlklx_id" IS '资料库类型编号';


